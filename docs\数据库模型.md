# TeaApp 数据库模型文档

## 概述

TeaApp 使用基于 uni-app 本地存储的数据库系统，模拟传统关系型数据库的功能。系统采用 JSON 格式存储数据，支持离线操作和数据同步。

**数据库信息：**
- 数据库名称：`tea_record_db`
- 当前版本：`1.1.0`
- 存储方式：uni-app 本地存储 (localStorage)
- 数据格式：JSON

## 数据表结构

### 1. 工作记录表 (workRecords)

**表名：** `workRecords`  
**描述：** 存储工人的日常工作记录，包括采茶和时工两种工作模式

#### 字段定义

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| `id` | String | ✓ | 记录唯一标识符 | "rec_20240115_001" |
| `date` | String | ✓ | 工作日期 (YYYY-MM-DD) | "2024-01-15" |
| `worker_name` | String | ✓ | 工人姓名 | "张三" |
| `work_mode` | String | ✓ | 工作模式：tea_picking/hourly | "tea_picking" |
| `total_earnings` | Number | ✓ | 总收入 | 150.50 |
| `tea_picking_details` | Array | - | 采茶详情（仅采茶模式） | 见下方详细结构 |
| `hourly_details` | Object | - | 时工详情（仅时工模式） | 见下方详细结构 |
| `created_at` | String | ✓ | 创建时间 (ISO 8601) | "2024-01-15T08:30:00.000Z" |
| `updated_at` | String | ✓ | 更新时间 (ISO 8601) | "2024-01-15T08:30:00.000Z" |
| `sync_status` | String | ✓ | 同步状态：pending/synced/error | "pending" |

#### 采茶详情结构 (tea_picking_details)

```json
[
  {
    "time_period": "morning",        // 时间段：morning
    "tea_type": "绿茶",              // 茶叶类型
    "unit_price": 2.5,              // 单价 (元/斤)
    "actual_weight": 60.0,          // 实际重量 (斤)
    "earnings": 150.0               // 收入 (单价 × 重量)
  }
]
```

#### 时工详情结构 (hourly_details)

```json
{
  "is_detail_mode": false,          // 是否详细模式
  "simple": {                       // 简化模式
    "work_hours": 8.0,              // 工作小时数
    "hourly_rate": 15.0             // 时薪
  },
  "detail": {                       // 详细模式
    "morning": {
      "work_hours": 4.0,
      "hourly_rate": 15.0
    },
    "afternoon": {
      "work_hours": 4.0,
      "hourly_rate": 15.0
    },
    "overtime": {
      "work_hours": 2.0,
      "hourly_rate": 20.0
    }
  }
}
```

### 2. 销售记录表 (sales_records)

**表名：** `sales_records`
**描述：** 存储茶叶销售记录，支持单客户和多客户销售模式

#### 字段定义

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| `id` | String | ✓ | 记录唯一标识符 | "sale_20240115_001" |
| `date` | String | ✓ | 销售日期 (YYYY-MM-DD) | "2024-01-15" |
| `sales_mode` | String | ✓ | 销售模式：single_customer/multiple_customers | "single_customer" |
| `work_record_id` | String | - | 关联工作记录ID（向后兼容） | "rec_20240115_001" |
| `customer_name` | String | ✓ | 客户姓名 | "客户A" |
| `selling_price` | Number | ✓ | 销售单价 (元/斤) | 3.0 |
| `production` | Number | ✓ | 销售产量 (斤) | 50.0 |
| `total_income` | Number | ✓ | 销售总收入 | 150.0 |
| `other_income` | Number | - | 其他收入 | 20.0 |
| `total_cost` | Number | ✓ | 总成本 | 100.0 |
| `other_cost` | Number | - | 其他支出 | 10.0 |
| `gross_profit` | Number | ✓ | 毛利润 | 60.0 |
| `notes` | String | - | 备注信息 | "优质茶叶" |
| `is_master_record` | Boolean | - | 是否为主记录（多客户模式用） | true |
| `master_record_id` | String | - | 主记录ID（多客户模式子记录用） | "sale_20240115_001" |
| `customer_details` | Array | - | 客户详情列表（多客户模式主记录用） | 见下方详细结构 |
| `allocation_strategy` | String | - | 产量分配策略：auto/manual | "auto" |
| `created_at` | String | ✓ | 创建时间 | "2024-01-15T08:30:00.000Z" |
| `updated_at` | String | ✓ | 更新时间 | "2024-01-15T08:30:00.000Z" |
| `sync_status` | String | ✓ | 同步状态 | "pending" |

#### 多客户模式客户详情结构 (customer_details)

```json
[
  {
    "customer_id": "cust_001",           // 客户ID
    "customer_name": "客户A",            // 客户姓名
    "selling_price": 3.0,               // 销售单价
    "production": 30.0,                 // 分配产量
    "total_income": 90.0,               // 销售收入
    "other_income": 5.0,                // 其他收入
    "other_cost": 2.0,                  // 其他支出
    "notes": "上午产量",                 // 备注
    "time_period_allocation": {          // 时间段分配
      "morning": 20.0,                  // 上午产量
      "afternoon": 10.0                 // 下午产量
    },
    "allocation_priority": 1             // 分配优先级
  }
]
```

### 3. 姓名库表 (name_library)

**表名：** `name_library`  
**描述：** 存储常用的工人和客户姓名，提供自动补全功能

#### 字段定义

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| `id` | String | ✓ | 记录唯一标识符 | "name_001" |
| `name` | String | ✓ | 姓名 | "张三" |
| `type` | String | ✓ | 类型：worker/customer/both | "worker" |
| `usage_count` | Number | ✓ | 使用次数 | 15 |
| `last_used` | String | ✓ | 最后使用时间 | "2024-01-15T08:30:00.000Z" |
| `created_at` | String | ✓ | 创建时间 | "2024-01-15T08:30:00.000Z" |
| `updated_at` | String | ✓ | 更新时间 | "2024-01-15T08:30:00.000Z" |

### 4. 同步队列表 (sync_queue)

**表名：** `sync_queue`  
**描述：** 存储待同步的数据操作记录

#### 字段定义

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| `id` | String | ✓ | 队列记录ID | "sync_001" |
| `operation` | String | ✓ | 操作类型：insert/update/delete | "insert" |
| `table_name` | String | ✓ | 目标表名 | "workRecords" |
| `record_id` | String | ✓ | 记录ID | "rec_20240115_001" |
| `data` | Object | ✓ | 操作数据 | {...} |
| `status` | String | ✓ | 状态：pending/syncing/success/failed | "pending" |
| `retry_count` | Number | ✓ | 重试次数 | 0 |
| `error_message` | String | - | 错误信息 | "网络连接失败" |
| `created_at` | String | ✓ | 创建时间 | "2024-01-15T08:30:00.000Z" |
| `updated_at` | String | ✓ | 更新时间 | "2024-01-15T08:30:00.000Z" |

### 5. 应用配置表 (app_config)

**表名：** `app_config`  
**描述：** 存储应用程序配置信息

#### 字段定义

| 字段名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| `id` | String | ✓ | 配置ID | "config_001" |
| `key` | String | ✓ | 配置键名 | "sync_interval" |
| `value` | Any | ✓ | 配置值 | 30000 |
| `type` | String | ✓ | 数据类型：string/number/boolean/object | "number" |
| `description` | String | - | 配置描述 | "同步间隔时间(毫秒)" |
| `created_at` | String | ✓ | 创建时间 | "2024-01-15T08:30:00.000Z" |
| `updated_at` | String | ✓ | 更新时间 | "2024-01-15T08:30:00.000Z" |

#### 默认配置项

```json
{
  "sync_interval": 30000,           // 同步间隔 (毫秒)
  "max_local_records": 10000,       // 最大本地记录数
  "enable_offline_mode": true,      // 启用离线模式
  "sales_record_enabled": true,     // 启用销售记录功能
  "auto_create_sales_record": true, // 自动创建销售记录
  "default_selling_price": 3.0      // 默认销售单价
}
```

## 数据关系

### 主要关系

1. **工作记录 → 销售记录 (1:N)**
   - 一个工作日期可以对应多个销售记录
   - 通过 `date` 字段关联
   - 向后兼容：通过 `work_record_id` 字段关联

2. **姓名库 → 工作记录 (1:N)**
   - 姓名库中的工人姓名可用于多个工作记录
   - 通过 `worker_name` 字段关联

3. **姓名库 → 销售记录 (1:N)**
   - 姓名库中的客户姓名可用于多个销售记录
   - 通过 `customer_name` 字段关联

### 关系图

```
工作记录表 (workRecords)
    ↓ (date)
销售记录表 (sales_records)
    ↑ (customer_name)
姓名库表 (name_library)
    ↓ (worker_name)
工作记录表 (workRecords)
```

## 业务规则

### 1. 销售模式规则

#### 单客户模式 (single_customer)
- 当日全部产量销售给一个客户
- 自动分配当日总产量
- 简化的数据结构和界面
- 适用于大部分常规销售场景

#### 多客户模式 (multiple_customers)
- 支持将当日产量分配给多个客户
- 使用主从记录结构存储数据
- 主记录存储汇总信息和客户详情列表
- 子记录存储各客户的具体销售信息

### 2. 产量分配规则

#### 智能分配算法
- **第一客户：** 自动分配上午产量
- **第二客户：** 自动分配下午产量
- **第三客户及以上：** 默认为0，需手动调整

#### 分配验证
- 各客户分配产量总和 ≤ 当日实际产量
- 支持手动调整分配比例
- 实时验证分配状态

#### 时间段产量计算
```javascript
// 时间段产量分配优先级
const allocationStrategy = {
  customer1: ['morning'],               // 上午
  customer2: ['afternoon'],             // 下午
  customer3: ['manual']                 // 手动分配
}
```

### 3. 收入计算规则

#### 单客户模式
- **销售收入** = 销售单价 × 销售产量
- **总收入** = 销售收入 + 其他收入
- **总成本** = 工人工钱 + 其他支出
- **毛利润** = 总收入 - 总成本

#### 多客户模式
- **客户销售收入** = 客户单价 × 客户产量
- **日总销售收入** = Σ(各客户销售收入)
- **日总收入** = 日总销售收入 + 其他收入
- **日总成本** = 工人工钱 + 各客户其他支出
- **日毛利润** = 日总收入 - 日总成本

### 4. 数据存储规则

#### 单客户模式存储
```json
{
  "id": "sale_20240115_001",
  "date": "2024-01-15",
  "sales_mode": "single_customer",
  "customer_name": "客户A",
  "selling_price": 3.0,
  "production": 50.0,
  "is_master_record": true
}
```

#### 多客户模式存储
```json
{
  "id": "sale_20240115_001",
  "date": "2024-01-15",
  "sales_mode": "multiple_customers",
  "is_master_record": true,
  "customer_details": [
    {
      "customer_name": "客户A",
      "selling_price": 3.0,
      "production": 30.0,
      "allocation_priority": 1
    },
    {
      "customer_name": "客户B",
      "selling_price": 3.2,
      "production": 20.0,
      "allocation_priority": 2
    }
  ]
}
```

### 5. 数据同步规则

- 所有数据操作都会添加到同步队列
- 支持离线操作，网络恢复后自动同步
- 同步失败的记录会自动重试
- 最大重试次数：3次

### 6. 数据验证规则

- 所有金额字段必须为非负数
- 日期格式必须为 YYYY-MM-DD
- 产量分配总和不能超过实际产量
- 必填字段不能为空
- 销售模式必须为有效值
- 多客户模式至少包含一个客户
- **时间段字段约束**：`time_period` 字段只能为 'morning' 或 'afternoon'
- **历史数据兼容**：系统自动将历史的 'noon' 数据映射到 'afternoon'

## 索引策略

### 主要索引

1. **工作记录表**
   - 主索引：`id`
   - 复合索引：`date + worker_name`
   - 时间索引：`created_at`

2. **销售记录表**
   - 主索引：`id`
   - 复合索引：`date + customer_name`
   - 外键索引：`work_record_id`

3. **姓名库表**
   - 主索引：`id`
   - 唯一索引：`name + type`
   - 排序索引：`usage_count DESC`

## 数据迁移

### 版本兼容性

- **v1.0.0 → v1.1.0**
  - 销售记录表新增 `date` 字段
  - 保持 `work_record_id` 字段向后兼容
  - 自动数据迁移脚本

### 迁移脚本

```javascript
// 自动迁移旧版本销售记录
function migrateSalesRecords() {
  const salesRecords = uni.getStorageSync('sales_records') || []
  const workRecords = uni.getStorageSync('workRecords') || []
  
  salesRecords.forEach(record => {
    if (!record.date && record.work_record_id) {
      const workRecord = workRecords.find(wr => wr.id === record.work_record_id)
      if (workRecord) {
        record.date = workRecord.date
      }
    }
  })
  
  uni.setStorageSync('sales_records', salesRecords)
}
```

## 性能优化

### 存储优化

- 定期清理过期的同步队列记录
- 限制本地记录数量上限 (默认10000条)
- 使用 JSON 压缩减少存储空间

### 查询优化

- 实现内存缓存机制
- 按日期范围分页查询
- 延迟加载非关键数据

### 同步优化

- 批量同步减少网络请求
- 增量同步只传输变更数据
- 智能重试机制避免无效请求

## 备份与恢复

### 数据备份

```javascript
// 导出所有数据
function exportAllData() {
  const tables = ['workRecords', 'sales_records', 'name_library', 'sync_queue', 'app_config']
  const backup = {
    version: '1.1.0',
    timestamp: new Date().toISOString(),
    data: {}
  }
  
  tables.forEach(table => {
    backup.data[table] = uni.getStorageSync(table) || []
  })
  
  return JSON.stringify(backup)
}
```

### 数据恢复

```javascript
// 导入备份数据
function importBackupData(backupJson) {
  const backup = JSON.parse(backupJson)
  
  Object.entries(backup.data).forEach(([table, data]) => {
    uni.setStorageSync(table, data)
  })
}
```

---

**文档版本：** 1.1.0  
**最后更新：** 2024-01-15  
**维护者：** TeaApp 开发团队
